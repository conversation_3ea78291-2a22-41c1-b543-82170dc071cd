import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { MessageCircle, Trophy, Sword, User, Settings, HelpCircle, Star, Zap, Users } from 'lucide-react';
import EnhancedMSG91ChatWidget from '@/components/EnhancedMSG91ChatWidget';
import { useFeatureFlag } from '@/utils/featureFlags';
import { useIsMobile } from '@/hooks/use-mobile';
import { useAuth } from '@/context/AuthContext';

export default function MorePage() {
  const navigate = useNavigate();
  const [isChatOpen, setIsChatOpen] = useState(false);
  const isMobile = useIsMobile();
  const { user } = useAuth();

  // Feature flags
  const tournamentsEnabled = useFeatureFlag('TOURNAMENTS');

  return (
    <div className={`min-h-screen bg-black relative ${isMobile ? 'pb-20' : ''}`}>
      {/* Sports Doodle Background Pattern */}
      <div
        className="absolute inset-0 opacity-[0.15] pointer-events-none"
        style={{
          backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23059669' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='4'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
          backgroundSize: '200px 200px',
          backgroundRepeat: 'repeat'
        }}
      />

      {isMobile ? (
        // Mobile-First App-Like Dashboard
        <div className="relative z-10">
          {/* Header Section */}
          <div className="bg-gradient-to-b from-emerald-900/20 to-transparent pt-16 pb-8">
            <div className="px-6">
              <div className="flex items-center mb-6">
                <div className="w-16 h-16 rounded-2xl bg-emerald-900/50 flex items-center justify-center mr-4">
                  <User className="w-8 h-8 text-emerald-400" />
                </div>
                <div>
                  <h1 className="text-2xl font-bold text-white">More Features</h1>
                  <p className="text-gray-300 text-sm">Explore additional tools & settings</p>
                </div>
              </div>

              {/* Quick Stats */}
              <div className="grid grid-cols-3 gap-3 mb-6">
                <div className="bg-gray-900/50 rounded-2xl p-4 text-center border border-emerald-900/20">
                  <Star className="w-6 h-6 text-yellow-400 mx-auto mb-2" />
                  <p className="text-xs text-gray-300">Level</p>
                  <p className="text-lg font-bold text-white">1</p>
                </div>
                <div className="bg-gray-900/50 rounded-2xl p-4 text-center border border-emerald-900/20">
                  <Zap className="w-6 h-6 text-emerald-400 mx-auto mb-2" />
                  <p className="text-xs text-gray-300">Streak</p>
                  <p className="text-lg font-bold text-white">4</p>
                </div>
                <div className="bg-gray-900/50 rounded-2xl p-4 text-center border border-emerald-900/20">
                  <Users className="w-6 h-6 text-blue-400 mx-auto mb-2" />
                  <p className="text-xs text-gray-300">Teams</p>
                  <p className="text-lg font-bold text-white">0</p>
                </div>
              </div>
            </div>
          </div>

          {/* Feature Cards */}
          <div className="px-6 space-y-4">
            {/* Chat Assistant Card */}
            <div className="bg-gray-900/50 rounded-2xl p-6 border border-emerald-900/20 backdrop-blur-sm">
              <div className="flex items-center mb-4">
                <div className="w-12 h-12 rounded-xl bg-blue-900/50 flex items-center justify-center mr-4">
                  <MessageCircle className="w-6 h-6 text-blue-400" />
                </div>
                <div className="flex-1">
                  <h2 className="text-xl font-bold text-white">Chat Assistant</h2>
                  <p className="text-sm text-gray-300">AI-powered support</p>
                </div>
              </div>
              <p className="text-gray-300 mb-4 text-sm leading-relaxed">
                Get instant help, ask questions, or chat with our AI assistant for support and guidance.
              </p>
              <button
                onClick={() => setIsChatOpen(true)}
                className="w-full bg-blue-900/80 text-white py-3 px-4 rounded-xl font-semibold hover:bg-blue-800/80 transition-colors min-h-[48px]"
              >
                Open Chat Assistant
              </button>
            </div>

            {/* Tournament Section Card - Only show when feature is enabled */}
            {tournamentsEnabled && (
              <div className="bg-gray-900/50 rounded-2xl p-6 border border-emerald-900/20 backdrop-blur-sm">
                <div className="flex items-center mb-4">
                  <div className="w-12 h-12 rounded-xl bg-yellow-900/50 flex items-center justify-center mr-4">
                    <Trophy className="w-6 h-6 text-yellow-400" />
                  </div>
                  <div className="flex-1">
                    <h2 className="text-xl font-bold text-white">Tournaments</h2>
                    <p className="text-sm text-gray-300">Competitive events</p>
                  </div>
                </div>
                <p className="text-gray-300 mb-4 text-sm leading-relaxed">
                  Browse, join, or host tournaments. View fixtures, results, and more in our tournament hub.
                </p>
                <button
                  onClick={() => navigate('/tournaments')}
                  className="w-full bg-yellow-900/80 text-white py-3 px-4 rounded-xl font-semibold hover:bg-yellow-800/80 transition-colors min-h-[48px]"
                >
                  Go to Tournaments
                </button>
              </div>
            )}

            {/* Challenge Mode Card */}
            <div className="bg-gray-900/50 rounded-2xl p-6 border border-emerald-900/20 backdrop-blur-sm">
              <div className="flex items-center mb-4">
                <div className="w-12 h-12 rounded-xl bg-emerald-900/50 flex items-center justify-center mr-4">
                  <Sword className="w-6 h-6 text-emerald-400" />
                </div>
                <div className="flex-1">
                  <h2 className="text-xl font-bold text-white">Challenge Mode</h2>
                  <p className="text-sm text-gray-300">Competitive arena</p>
                </div>
              </div>
              <p className="text-gray-300 mb-4 text-sm leading-relaxed">
                Create or join a team, challenge rivals, and climb the leaderboard in our competitive arena.
              </p>
              <button
                onClick={() => navigate('/challenge')}
                className="w-full bg-emerald-900/80 text-white py-3 px-4 rounded-xl font-semibold hover:bg-emerald-800/80 transition-colors min-h-[48px]"
              >
                Enter Challenge Mode
              </button>
            </div>

            {/* Quick Actions */}
            <div className="bg-gray-900/50 rounded-2xl p-6 border border-emerald-900/20 backdrop-blur-sm">
              <h3 className="text-lg font-bold text-white mb-4">Quick Actions</h3>
              <div className="grid grid-cols-2 gap-3">
                <button
                  onClick={() => navigate('/profile')}
                  className="flex flex-col items-center p-4 bg-gray-800/50 rounded-xl hover:bg-gray-700/50 transition-colors min-h-[80px]"
                >
                  <Settings className="w-6 h-6 text-gray-400 mb-2" />
                  <span className="text-sm text-gray-300">Settings</span>
                </button>
                <button
                  onClick={() => navigate('/help')}
                  className="flex flex-col items-center p-4 bg-gray-800/50 rounded-xl hover:bg-gray-700/50 transition-colors min-h-[80px]"
                >
                  <HelpCircle className="w-6 h-6 text-gray-400 mb-2" />
                  <span className="text-sm text-gray-300">Help</span>
                </button>
              </div>
            </div>
          </div>
        </div>
      ) : (
        // Desktop Design (unchanged)
        <div className="min-h-screen bg-gradient-to-b from-navy-dark to-navy-light text-white flex flex-col items-center py-10 px-4">
          <h1 className="text-3xl font-bold mb-8">More</h1>
          <div className="w-full max-w-md space-y-6">
            {/* Chat Assistant Card */}
            <div className="bg-card border border-navy rounded-xl p-6 flex flex-col items-center shadow-lg">
              <MessageCircle className="w-10 h-10 text-indigo mb-3" />
              <h2 className="text-xl font-semibold mb-2">Chat-assistant</h2>
              <p className="text-muted-foreground mb-4 text-center">Get instant help, ask questions, or chat with our AI assistant for support and guidance.</p>
              <button onClick={() => setIsChatOpen(true)} className="bg-indigo text-white px-6 py-2 rounded-lg font-medium hover:bg-indigo-dark transition">Open Chat</button>
            </div>
            {/* Tournament Section Card - Only show when feature is enabled */}
            {tournamentsEnabled && (
              <div className="bg-card border border-navy rounded-xl p-6 flex flex-col items-center shadow-lg">
                <Trophy className="w-10 h-10 text-yellow-400 mb-3" />
                <h2 className="text-xl font-semibold mb-2">Tournament Section</h2>
                <p className="text-muted-foreground mb-4 text-center">Browse, join, or host tournaments. View fixtures, results, and more in our tournament hub.</p>
                <button onClick={() => navigate('/tournaments')} className="bg-yellow-400 text-navy px-6 py-2 rounded-lg font-medium hover:bg-yellow-500 transition">Go to Tournaments</button>
              </div>
            )}
            {/* Challenge Mode Card */}
            <div className="bg-card border border-navy rounded-xl p-6 flex flex-col items-center shadow-lg">
              <Sword className="w-10 h-10 text-emerald-400 mb-3" />
              <h2 className="text-xl font-semibold mb-2">Challenge Mode</h2>
              <p className="text-muted-foreground mb-4 text-center">Create or join a team, challenge rivals, and climb the leaderboard in our competitive arena.</p>
              <button onClick={() => navigate('/challenge')} className="bg-emerald-400 text-navy px-6 py-2 rounded-lg font-medium hover:bg-emerald-500 transition">Enter Challenge Mode</button>
            </div>
          </div>
        </div>
      )}

      {/* Enhanced MSG91 Chat Widget */}
      <EnhancedMSG91ChatWidget isOpen={isChatOpen} setIsOpen={setIsChatOpen} />
    </div>
  );
}
