import React, { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { CalendarIcon, UsersIcon, ArrowLeft, RefreshCw, TrendingUp, BarChart3, Target, Star } from 'lucide-react';
import { format, startOfMonth, endOfMonth, subMonths, parseISO, isWithinInterval, addDays } from 'date-fns';
import { toast } from '@/components/ui/use-toast';
import { useAuth } from '@/context/AuthContext';
import { useNavigate } from 'react-router-dom';
import PaymentMethodFilter, { PaymentMethodFilterType } from '@/components/admin/PaymentMethodFilter';

interface BookingData {
  id: string;
  booking_date: string;
  start_time: string;
  end_time: string;
  total_price: number;
  status: string;
  payment_method: string;
  court: {
    name: string;
    venue_id: string;
    sport_id: string;
    sports: {
      name: string;
    };
    venues: {
      name?: string;
    };
    platform_fee_percentage: number;
  };
}

const AnalyticsDashboard_Mobile: React.FC = () => {
  const { userRole } = useAuth();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);
  const [bookings, setBookings] = useState<BookingData[]>([]);
  const [adminVenues, setAdminVenues] = useState<Array<{ venue_id: string }>>([]);
  const [timeRange, setTimeRange] = useState('month');
  const [selectedVenueId, setSelectedVenueId] = useState<string>('all');
  const [venues, setVenues] = useState<Array<{ id: string, name: string }>>([]);
  const [currentDate, setCurrentDate] = useState(new Date());
  const [paymentMethodFilter, setPaymentMethodFilter] = useState<PaymentMethodFilterType>('online');

  useEffect(() => {
    const fetchVenues = async () => {
      if (userRole === 'admin') {
        const { data: venueData, error: venueError } = await supabase
          .rpc('get_admin_venues');
        if (venueError) return;
        setAdminVenues(venueData || []);
      }
    };
    fetchVenues();
  }, [userRole]);

  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      try {
        const { data: venuesData, error: venuesError } = await supabase
          .from('venues')
          .select('id, name, platform_fee_percentage')
          .eq('is_active', true);
        if (venuesError) throw venuesError;

        // Filter out error objects from venuesData
        const validVenuesData = (venuesData || []).filter((v: any) => v && typeof v === 'object' && 'id' in v && 'name' in v && 'platform_fee_percentage' in v);
        setVenues(validVenuesData.map((v: any) => ({ id: v.id, name: v.name })));

        // Get venue IDs that this admin manages
        const venueIds = adminVenues.map(v => v.venue_id);
        
        if (venueIds.length === 0 && userRole === 'admin') {
          setBookings([]);
          setLoading(false);
          return;
        }

        // Get courts for these venues
        let courtsQuery = supabase
          .from('courts')
          .select('id, venue_id, name');

        if (userRole === 'admin' && venueIds.length > 0) {
          courtsQuery = courtsQuery.in('venue_id', venueIds);
        }

        const { data: courts, error: courtsError } = await courtsQuery;
        if (courtsError) throw courtsError;

        const courtIds = courts?.map(c => c.id) || [];

        if (courtIds.length === 0) {
          setBookings([]);
          setLoading(false);
          return;
        }

        // Fetch bookings with related data
        const { data: bookingsData, error: bookingsError } = await supabase
          .from('bookings')
          .select(`
            id,
            booking_date,
            start_time,
            end_time,
            total_price,
            status,
            payment_method,
            court_id
          `)
          .in('court_id', courtIds)
          .in('status', ['confirmed', 'completed']);

        if (bookingsError) throw bookingsError;

        // Process bookings to add court and venue information
        const processedBookings = (bookingsData || []).map(booking => {
          const court = courts?.find(c => c.id === booking.court_id);
          const venue = validVenuesData.find((v: any) => v.id === court?.venue_id);
          
          return {
            ...booking,
            court: {
              name: court?.name || '',
              venue_id: court?.venue_id || '',
              sport_id: '',
              sports: { name: '' },
              venues: { name: (venue != null && typeof venue === 'object' && 'name' in venue) ? (venue as any).name : '' },
              platform_fee_percentage: (venue != null && typeof venue === 'object' && 'platform_fee_percentage' in venue) ? (venue as any).platform_fee_percentage : 5
            }
          };
        }) as BookingData[];

        setBookings(processedBookings);
      } catch (error) {
        console.error('Error fetching analytics data:', error);
        toast({
          title: 'Error',
          description: 'Failed to fetch analytics data',
          variant: 'destructive',
        });
      } finally {
        setLoading(false);
      }
    };
    
    if (adminVenues.length > 0 || userRole === 'super_admin') {
      fetchData();
    }
  }, [adminVenues, userRole]);

  useEffect(() => {
    if (userRole === 'admin' && adminVenues.length > 0) {
      setSelectedVenueId(adminVenues[0].venue_id);
    }
  }, [userRole, adminVenues]);

  const getPaymentMethodFilteredBookings = (bookings: BookingData[]) => {
    if (paymentMethodFilter === 'all') return bookings;
    
    return bookings.filter(booking => {
      const method = booking.payment_method || 'online';
      if (paymentMethodFilter === 'online') {
        return method === 'online';
      } else {
        return method === 'cash' || method === 'card';
      }
    });
  };

  const filteredBookings = getPaymentMethodFilteredBookings(
    bookings.filter(booking => {
      const bookingDate = parseISO(booking.booking_date);
      let rangeStart, rangeEnd;
      switch(timeRange) {
        case 'week':
          rangeStart = addDays(currentDate, -7);
          rangeEnd = currentDate;
          break;
        case 'month':
          rangeStart = startOfMonth(currentDate);
          rangeEnd = endOfMonth(currentDate);
          break;
        case 'year':
          rangeStart = new Date(currentDate.getFullYear(), 0, 1);
          rangeEnd = new Date(currentDate.getFullYear(), 11, 31);
          break;
        default:
          rangeStart = startOfMonth(currentDate);
          rangeEnd = endOfMonth(currentDate);
      }
      const isInDateRange = isWithinInterval(bookingDate, { start: rangeStart, end: rangeEnd });
      const isMatchingVenue = selectedVenueId === 'all' || booking.court?.venue_id === selectedVenueId;
      return isInDateRange && isMatchingVenue;
    })
  );

  const totalRevenue = filteredBookings.reduce((sum, booking) => sum + booking.total_price, 0);
  const totalPlatformFee = filteredBookings.reduce((sum, booking) => {
    const feePercent = booking.court?.platform_fee_percentage ?? 5;
    return sum + (booking.total_price * (feePercent / 100));
  }, 0);
  const totalNetRevenue = totalRevenue - totalPlatformFee;

  const handlePreviousPeriod = () => {
    switch(timeRange) {
      case 'week':
        setCurrentDate(prev => addDays(prev, -7));
        break;
      case 'month':
        setCurrentDate(prev => subMonths(prev, 1));
        break;
      case 'year':
        setCurrentDate(prev => new Date(prev.getFullYear() - 1, prev.getMonth(), prev.getDate()));
        break;
    }
  };

  const handleNextPeriod = () => {
    const today = new Date();
    let newDate;
    switch(timeRange) {
      case 'week':
        newDate = addDays(currentDate, 7);
        break;
      case 'month':
        newDate = new Date(currentDate.getFullYear(), currentDate.getMonth() + 1, 1);
        break;
      case 'year':
        newDate = new Date(currentDate.getFullYear() + 1, currentDate.getMonth(), currentDate.getDate());
        break;
      default:
        newDate = new Date();
    }
    if (newDate <= today) {
      setCurrentDate(newDate);
    }
  };

  const getTimeRangeLabel = () => {
    switch(timeRange) {
      case 'week':
        return `${format(addDays(currentDate, -7), 'MMM dd')} - ${format(currentDate, 'MMM dd, yyyy')}`;
      case 'month':
        return format(currentDate, 'MMMM yyyy');
      case 'year':
        return format(currentDate, 'yyyy');
      default:
        return format(currentDate, 'MMMM yyyy');
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-950 via-slate-900 to-black flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-emerald-500"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-b from-emerald-900/20 to-black relative overflow-hidden pb-16">
      {/* Background Pattern */}
      <div className="absolute inset-0 bg-[url('https://images.unsplash.com/photo-1485395037613-e83d5c1f5290?ixlib=rb-1.2.1&auto=format&fit=crop&w=1350&q=80')] opacity-5 bg-center bg-cover"></div>

      {/* Header - Mobile-First Design */}
      <div className="sticky top-0 z-10 bg-gradient-to-r from-emerald-600 to-emerald-800 shadow-xl border-b border-emerald-400/30">
        <div className="flex items-center justify-between px-4 py-4">
          <div className="flex items-center">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => navigate('/admin/mobile-home')}
              className="mr-3 p-2 rounded-full hover:bg-white/10 transition-all duration-300 min-h-[48px] min-w-[48px] flex items-center justify-center"
            >
              <ArrowLeft className="h-6 w-6 text-white" />
            </Button>
            <div>
              <h1 className="text-xl font-bold text-white tracking-wide">Analytics Dashboard</h1>
              <p className="text-emerald-200 text-sm opacity-90">Track your venue performance and booking trends</p>
            </div>
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => window.location.reload()}
            className="text-white hover:bg-white/10 min-h-[48px] min-w-[48px] rounded-full"
          >
            <RefreshCw className="h-5 w-5" />
          </Button>
        </div>
      </div>

      {/* Main Content - Mobile-First Container */}
      <div className="container mx-auto px-4 py-6 space-y-6 relative z-10">
      <div className="flex flex-col gap-4">

        {/* Mobile-Optimized Controls */}
        <div className="bg-gray-900/50 rounded-2xl overflow-hidden shadow-xl border border-emerald-900/20 hover:border-emerald-700/50 backdrop-blur-sm transition-all duration-300">
          <div className="p-6">
            <div className="flex items-center mb-4">
              <div className="w-12 h-12 bg-emerald-500/20 rounded-xl flex items-center justify-center mr-4 border border-emerald-500/30">
                <BarChart3 className="w-6 h-6 text-emerald-400" />
              </div>
              <div>
                <h3 className="text-lg font-bold text-white mb-1">Filters & Controls</h3>
                <p className="text-sm text-gray-300 opacity-90">Customize your analytics view</p>
              </div>
            </div>

            <div className="space-y-4">
              {/* Payment Method Filter */}
              <div>
                <label className="text-sm font-medium text-emerald-300 mb-2 block">Payment Method</label>
                <PaymentMethodFilter
                  selectedFilter={paymentMethodFilter}
                  onFilterChange={setPaymentMethodFilter}
                />
              </div>

              {/* Venue Selector */}
              <div>
                <label className="text-sm font-medium text-emerald-300 mb-2 block">Venue</label>
                <select
                  value={selectedVenueId}
                  onChange={(e) => setSelectedVenueId(e.target.value)}
                  className="w-full px-4 py-3 bg-slate-800/50 border border-emerald-500/30 rounded-xl text-white focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 transition-all min-h-[44px]"
                >
                  {userRole === 'super_admin' && (
                    <option value="all">All Venues</option>
                  )}
                  {venues
                    .filter(venue =>
                      userRole === 'super_admin' ||
                      adminVenues.some(v => v.venue_id === venue.id)
                    )
                  .map(venue => (
                    <option key={venue.id} value={venue.id}>{venue.name}</option>
                  ))
                }
              </select>
            </div>
              {/* Time Range Navigation */}
              <div>
                <label className="text-sm font-medium text-emerald-300 mb-2 block">Time Period</label>
                <div className="flex items-center bg-slate-800/50 border border-emerald-500/30 rounded-xl overflow-hidden">
                  <Button
                    variant="ghost"
                    onClick={handlePreviousPeriod}
                    className="border-r border-emerald-500/20 text-white hover:bg-emerald-500/20 h-12 px-4 min-h-[48px]"
                  >
                    ←
                  </Button>
                  <div className="flex-1 px-4 text-center">
                    <span className="text-sm font-medium text-white">{getTimeRangeLabel()}</span>
                  </div>
                  <Button
                    variant="ghost"
                    onClick={handleNextPeriod}
                    className="border-l border-emerald-500/20 text-white hover:bg-emerald-500/20 h-12 px-4 min-h-[48px]"
                    disabled={
                      (timeRange === 'month' &&
                        currentDate.getMonth() === new Date().getMonth() &&
                        currentDate.getFullYear() === new Date().getFullYear()) ||
                      (timeRange === 'year' &&
                        currentDate.getFullYear() === new Date().getFullYear())
                    }
                  >
                    →
                  </Button>
                </div>
              </div>

              {/* Time Range Selector */}
              <div>
                <label className="text-sm font-medium text-emerald-300 mb-2 block">View</label>
                <div className="grid grid-cols-3 gap-2 bg-slate-800/50 border border-emerald-500/30 rounded-xl p-2">
                  <Button
                    variant={timeRange === 'week' ? 'default' : 'ghost'}
                    className={`rounded-lg h-10 text-sm transition-all min-h-[44px] ${
                      timeRange === 'week'
                        ? 'bg-emerald-500 text-white shadow-lg'
                        : 'text-emerald-200 hover:bg-emerald-500/20'
                    }`}
                    onClick={() => setTimeRange('week')}
                  >
                    Week
                  </Button>
                  <Button
                    variant={timeRange === 'month' ? 'default' : 'ghost'}
                    className={`rounded-lg h-10 text-sm transition-all min-h-[44px] ${
                      timeRange === 'month'
                        ? 'bg-emerald-500 text-white shadow-lg'
                        : 'text-emerald-200 hover:bg-emerald-500/20'
                    }`}
                    onClick={() => setTimeRange('month')}
                  >
                    Month
                  </Button>
                  <Button
                    variant={timeRange === 'year' ? 'default' : 'ghost'}
                    className={`rounded-lg h-10 text-sm transition-all min-h-[44px] ${
                      timeRange === 'year'
                        ? 'bg-emerald-500 text-white shadow-lg'
                        : 'text-emerald-200 hover:bg-emerald-500/20'
                    }`}
                    onClick={() => setTimeRange('year')}
                  >
                    Year
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
        {/* Key Metrics - Mobile-First Design */}
        <div className="space-y-4">
          <div className="flex items-center mb-4">
            <div className="w-12 h-12 bg-purple-500/20 rounded-xl flex items-center justify-center mr-4 border border-purple-500/30">
              <TrendingUp className="w-6 h-6 text-purple-400" />
            </div>
            <div>
              <h3 className="text-lg font-bold text-white mb-1">Key Metrics</h3>
              <p className="text-sm text-gray-300 opacity-90">Performance overview for {getTimeRangeLabel()}</p>
            </div>
          </div>

          <div className="grid grid-cols-1 gap-4">
            {/* Bookings Card */}
            <div className="bg-gray-900/50 rounded-2xl overflow-hidden shadow-xl border border-emerald-900/20 hover:border-emerald-700/50 backdrop-blur-sm transition-all duration-300">
              <div className="p-6">
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center gap-3">
                    <div className="w-12 h-12 rounded-xl bg-gradient-to-br from-emerald-500/20 to-emerald-600/10 border border-emerald-500/30 flex items-center justify-center">
                      <CalendarIcon className="w-6 h-6 text-emerald-400" />
                    </div>
                    <div>
                      <p className="text-sm font-medium text-emerald-300">Total Bookings</p>
                      <p className="text-xs text-emerald-200/70">For {getTimeRangeLabel()}</p>
                    </div>
                  </div>
                  <div className="text-right">
                    <h3 className="text-3xl font-bold text-white">{filteredBookings.length}</h3>
                    {timeRange === 'month' && (
                      <p className="text-xs text-emerald-200/70">this month</p>
                    )}
                  </div>
                </div>
              </div>
            </div>

            {/* Revenue Card */}
            <div className="bg-gray-900/50 rounded-2xl overflow-hidden shadow-xl border border-blue-900/20 hover:border-blue-700/50 backdrop-blur-sm transition-all duration-300">
              <div className="p-6">
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center gap-3">
                    <div className="w-12 h-12 rounded-xl bg-gradient-to-br from-blue-500/20 to-blue-600/10 border border-blue-500/30 flex items-center justify-center">
                      <span className="text-blue-400 font-bold text-lg">₹</span>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-blue-300">Revenue</p>
                      <p className="text-xs text-blue-200/70">For {getTimeRangeLabel()}</p>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="space-y-1">
                      <div className="flex items-baseline gap-2">
                        <h3 className="text-2xl font-bold text-white">₹{totalRevenue.toLocaleString()}</h3>
                        <span className="text-xs text-blue-200/70">Gross</span>
                      </div>
                      <div className="flex items-baseline gap-2">
                        <h4 className="text-xl font-semibold text-blue-400">₹{totalNetRevenue.toLocaleString()}</h4>
                        <span className="text-xs text-blue-200/70">Net</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            {/* Average Booking Value Card */}
            <div className="bg-gray-900/50 rounded-2xl overflow-hidden shadow-xl border border-orange-900/20 hover:border-orange-700/50 backdrop-blur-sm transition-all duration-300">
              <div className="p-6">
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center gap-3">
                    <div className="w-12 h-12 rounded-xl bg-gradient-to-br from-orange-500/20 to-orange-600/10 border border-orange-500/30 flex items-center justify-center">
                      <UsersIcon className="w-6 h-6 text-orange-400" />
                    </div>
                    <div>
                      <p className="text-sm font-medium text-orange-300">Avg Booking Value</p>
                      <p className="text-xs text-orange-200/70">Per booking</p>
                    </div>
                  </div>
                  <div className="text-right">
                    <h3 className="text-3xl font-bold text-white">
                      ₹{filteredBookings.length > 0
                        ? (totalRevenue / filteredBookings.length).toLocaleString(undefined, {
                            maximumFractionDigits: 2
                          })
                        : 0}
                    </h3>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AnalyticsDashboard_Mobile;
