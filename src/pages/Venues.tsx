import React, { useState, useEffect, useMemo } from 'react';
import { MapPin, Star, Filter, Search, Navigation, Clock, ArrowUpDown } from 'lucide-react';
import { useNavigate, useLocation } from 'react-router-dom';
import Header from '../components/Header';
// Removed BookSlotModal - now using BookingPage navigation
import { supabase } from '@/integrations/supabase/client';
import { useEnhancedLocation, calculateDistance } from '@/hooks/use-enhanced-location';
import { useSmartLocation } from '@/hooks/use-smart-location';
import { toast } from 'react-hot-toast';
import { useIsMobile } from '@/hooks/use-mobile';
import { getVenueDiscounts, VenueDiscount } from '@/utils/discountUtils';
import DiscountBadge from '@/components/DiscountBadge';
import LazyImage from '@/components/LazyImage';

interface Venue {
  id: string;
  name: string;
  location: string;
  description: string;
  image_url: string;
  rating: number;
  latitude: number | null;
  longitude: number | null;
  facilities?: string[];
  distance?: number | null;
}

interface Sport {
  id: string;
  name: string;
}

const Venues: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const isMobile = useIsMobile();

  // Get URL parameters for smart location resolution (memoized to prevent infinite loops)
  const urlLocationParams = useMemo(() => {
    const params = new URLSearchParams(location.search);
    const urlParams = {
      lat: params.get('lat') || undefined,
      lng: params.get('lng') || undefined,
      location: params.get('location') || undefined
    };

    if (import.meta.env.DEV && (urlParams.lat || urlParams.lng || urlParams.location)) {
      console.log('Venues: URL parameters detected:', urlParams);
    }

    return urlParams;
  }, [location.search]);

  // Use smart location system
  const smartLocation = useSmartLocation(urlLocationParams);

  // Keep legacy location data for fallback
  const { data: locationData, hasPermission } = useEnhancedLocation();
  // Removed isBookModalOpen state - now using navigation to BookingPage
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedSports, setSelectedSports] = useState<string[]>([]);
  const [isFilterOpen, setIsFilterOpen] = useState(false);
  const [ratingFilter, setRatingFilter] = useState(0);
  const [venues, setVenues] = useState<Venue[]>([]);
  const [sports, setSports] = useState<Sport[]>([]);
  const [loading, setLoading] = useState(true);
  const [sortOption, setSortOption] = useState<'distance' | 'rating'>('distance');
  const [venueDiscounts, setVenueDiscounts] = useState<Record<string, VenueDiscount>>({});

  useEffect(() => {
    const params = new URLSearchParams(location.search);
    const sportId = params.get('sport');

    if (sportId) {
      setSelectedSports([sportId]);
    }

    fetchVenues();
    fetchSports();
  }, [location.search, smartLocation.coordinates?.latitude, smartLocation.coordinates?.longitude, smartLocation.source]);

  const fetchVenues = async () => {
    try {
      setLoading(true);

      const params = new URLSearchParams(location.search);
      const sportId = params.get('sport');
      const locationParam = params.get('location');

      // Use URL, GPS and user preference coordinates (not fallback IP location) for venue recommendations
      const referenceCoordinates = (smartLocation.source === 'url' || smartLocation.source === 'gps' || smartLocation.source === 'preference') ? smartLocation.coordinates : null;

      if (import.meta.env.DEV) {
        console.log('SmartLocation data in Venues:', {
          source: smartLocation.source,
          coordinates: smartLocation.coordinates,
          name: smartLocation.name,
          isLoading: smartLocation.isLoading
        });

        if (referenceCoordinates) {
          console.log(`Using ${smartLocation.source} coordinates for venue filtering:`, referenceCoordinates);
        } else {
          console.log('No reference coordinates available for distance calculation');
        }
      }

      let query = supabase
        .from('venues')
        .select(`
          id,
          name,
          location,
          description,
          image_url,
          rating,
          latitude,
          longitude
        `)
        .eq('is_active', true);

      if (sportId) {
        const { data: courtData, error: courtError } = await supabase
          .from('courts')
          .select('venue_id')
          .eq('sport_id', sportId)
          .eq('is_active', true);

        if (courtError) throw courtError;

        if (courtData && courtData.length > 0) {
          const venueIds = courtData.map(court => court.venue_id);
          query = query.in('id', venueIds);
        } else {
          setVenues([]);
          setLoading(false);
          return;
        }
      }

      const { data, error } = await query;

      if (error) throw error;

      if (data) {
        const venuesWithDistance = data.map(venue => {
          const distance = referenceCoordinates ? calculateDistance(
            referenceCoordinates.latitude,
            referenceCoordinates.longitude,
            venue.latitude,
            venue.longitude
          ) : null;

          if (import.meta.env.DEV && referenceCoordinates && distance !== null) {
            console.log(`Distance calculated for ${venue.name}: ${distance.toFixed(2)}km`);
          }

          return {
            ...venue,
            distance: distance,
            facilities: [] as string[]
          };
        });

        // Filter venues within reasonable distance if coordinates are available
        let filteredVenues = venuesWithDistance;
        if (referenceCoordinates && locationParam) {
          // Filter venues within 25km radius when a specific location is selected
          const maxDistance = 25; // km
          filteredVenues = venuesWithDistance.filter(venue =>
            venue.distance === null || venue.distance <= maxDistance
          );

          if (import.meta.env.DEV) {
            console.log(`Filtered ${venuesWithDistance.length} venues to ${filteredVenues.length} within ${maxDistance}km of selected location`);
          }
        }

        // Sort venues by distance or rating
        if (referenceCoordinates) {
          filteredVenues.sort((a, b) => {
            if (a.distance === null) return 1;
            if (b.distance === null) return -1;
            return a.distance - b.distance;
          });
        } else {
          filteredVenues.sort((a, b) => (b.rating || 0) - (a.rating || 0));
        }

        for (const venue of filteredVenues) {
          const { data: courtsData, error: courtsError } = await supabase
            .from('courts')
            .select(`
              sports:sport_id (
                id,
                name
              )
            `)
            .eq('venue_id', venue.id)
            .eq('is_active', true);

          if (!courtsError && courtsData) {
            const facilities = Array.from(
              new Set(courtsData.map(court => court.sports?.name || ''))
            ).filter(Boolean);

            venue.facilities = facilities as string[];
          }
        }

        setVenues(filteredVenues);

        // Fetch discounts for filtered venues
        const venueIds = filteredVenues.map(venue => venue.id);
        if (venueIds.length > 0) {
          const discounts = await getVenueDiscounts(venueIds);
          setVenueDiscounts(discounts);
        }
      }
    } catch (error) {
      console.error('Error fetching venues:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchSports = async () => {
    try {
      const { data, error } = await supabase
        .from('sports')
        .select('id, name')
        .eq('is_active', true);
        
      if (error) throw error;
      
      if (data) {
        setSports(data);
      }
    } catch (error) {
      console.error('Error fetching sports:', error);
    }
  };

  const toggleSportFilter = (sport: string) => {
    if (selectedSports.includes(sport)) {
      setSelectedSports(selectedSports.filter(s => s !== sport));
    } else {
      setSelectedSports([...selectedSports, sport]);
    }
  };

  const clearFilters = () => {
    setSelectedSports([]);
    setRatingFilter(0);
    setSearchTerm('');
    navigate('/venues');
  };

  const applyFilters = () => {
    setIsFilterOpen(false);
    if (selectedSports.length === 1) {
      navigate(`/venues?sport=${selectedSports[0]}`);
    } else {
      fetchVenues();
    }
  };

  const toggleSortOption = () => {
    const newSortOption = sortOption === 'distance' ? 'rating' : 'distance';
    setSortOption(newSortOption);

    const sortedVenues = [...venues];
    // Allow distance sorting for URL, GPS and user preference, but not IP fallback location
    if (newSortOption === 'distance' && (smartLocation.source === 'url' || smartLocation.source === 'gps' || smartLocation.source === 'preference') && smartLocation.coordinates) {
      sortedVenues.sort((a, b) => {
        if (a.distance === null) return 1;
        if (b.distance === null) return -1;
        return a.distance - b.distance;
      });
    } else {
      sortedVenues.sort((a, b) => (b.rating || 0) - (a.rating || 0));
    }

    setVenues(sortedVenues);
  };

  const enableLocation = () => {
    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition(
        (position) => {
          // Location access granted - refresh venues without toast
          setTimeout(() => fetchVenues(), 1000);
        },
        (error) => {
          // Only show error toast for critical failures
          toast.error("Location access denied");
        }
      );
    }
  };

  const filteredVenues = venues.filter(venue => {
    const matchesSearch = venue.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         venue.location.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         (venue.description && venue.description.toLowerCase().includes(searchTerm.toLowerCase()));
    
    const matchesSport = selectedSports.length === 0 || 
                        (venue.facilities && venue.facilities.some(facility => 
                          sports
                            .filter(sport => selectedSports.includes(sport.id))
                            .map(sport => sport.name)
                            .includes(facility)
                        ));
    
    const matchesRating = venue.rating >= ratingFilter;
    
    return matchesSearch && matchesSport && matchesRating;
  });

  // Sports doodle pattern SVG for consistency with Index.tsx
  const sportsDoodlePattern = `url("data:image/svg+xml,%3Csvg width='200' height='200' viewBox='0 0 200 200' xmlns='http://www.w3.org/2000/svg'%3E%3Cdefs%3E%3Cstyle%3E.doodle%7Bfill:none;stroke-width:1.5;%7D.grey%7Bstroke:%23666666;%7D.green%7Bstroke:%2310b981;%7D%3C/style%3E%3C/defs%3E%3C!-- Football/Soccer --%3E%3Ccircle cx='25' cy='30' r='8' class='doodle grey'/%3E%3Cpath d='M20 25l10 0M17 30l16 0M20 35l10 0' class='doodle grey'/%3E%3C!-- Basketball --%3E%3Ccircle cx='160' cy='45' r='9' class='doodle green'/%3E%3Cpath d='M151 45h18M160 36v18M155 40l10 10M155 50l10-10' class='doodle green'/%3E%3C!-- Tennis Racket --%3E%3Cellipse cx='70' cy='25' rx='6' ry='10' class='doodle grey'/%3E%3Cpath d='M70 35l0 8' class='doodle grey'/%3E%3Cpath d='M64 25h12M70 20v10' class='doodle grey'/%3E%3C!-- Cricket Bat --%3E%3Cpath d='M130 20l8 0l0 15l-8 0z' class='doodle green'/%3E%3Cpath d='M134 35l0 8' class='doodle green'/%3E%3C!-- Badminton Shuttlecock --%3E%3Cpath d='M45 70l0-8l6 4l6-4l0 8l-6 4z' class='doodle grey'/%3E%3Cpath d='M51 62l0-5' class='doodle grey'/%3E%3C!-- Swimming --%3E%3Cpath d='M15 85c3 0 6-2 9-2s6 2 9 2s6-2 9-2s6 2 9 2' class='doodle green'/%3E%3Cpath d='M15 90c3 0 6-2 9-2s6 2 9 2s6-2 9-2s6 2 9 2' class='doodle green'/%3E%3C!-- Volleyball --%3E%3Ccircle cx='170' cy='85' r='8' class='doodle grey'/%3E%3Cpath d='M162 85h16M170 77v16M165 80l10 10' class='doodle grey'/%3E%3C!-- Running Figure --%3E%3Ccircle cx='95' cy='75' r='3' class='doodle green'/%3E%3Cpath d='M95 78l0 12M95 85l-5 8M95 85l5 8M95 82l-4-2M95 82l6 2' class='doodle green'/%3E%3C!-- Table Tennis Paddle --%3E%3Cellipse cx='125' cy='80' rx='5' ry='7' class='doodle grey'/%3E%3Cpath d='M125 87l0 6' class='doodle grey'/%3E%3C!-- Hockey Stick --%3E%3Cpath d='M30 120l15 0l0 3l-12 0l0 8' class='doodle green'/%3E%3C!-- Golf Club --%3E%3Cpath d='M180 120l0 15M180 135l-3 0' class='doodle grey'/%3E%3Ccircle cx='177' cy='138' r='1.5' class='doodle grey'/%3E%3C!-- Boxing Glove --%3E%3Cpath d='M65 115c0-3 3-5 7-5s7 2 7 5l0 8c0 3-3 5-7 5s-7-2-7-5z' class='doodle green'/%3E%3Cpath d='M65 120l-3 0l0 5l3 0' class='doodle green'/%3E%3C!-- Dumbbell --%3E%3Cpath d='M110 115l0 10M107 115l6 0M107 125l6 0M105 113l0 4M115 113l0 4M105 125l0 4M115 125l0 4' class='doodle grey'/%3E%3C!-- Whistle --%3E%3Cellipse cx='150' cy='120' rx='4' ry='2' class='doodle green'/%3E%3Ccircle cx='148' cy='120' r='1' class='doodle green'/%3E%3Cpath d='M154 120l3 0' class='doodle green'/%3E%3C!-- Trophy --%3E%3Cpath d='M25 155l0 8l10 0l0-8M30 155l0-5M27 150l6 0M25 163l10 0M27 166l6 0' class='doodle grey'/%3E%3Cpath d='M22 152l3 0M35 152l3 0' class='doodle grey'/%3E%3C!-- Medal --%3E%3Ccircle cx='170' cy='160' r='5' class='doodle green'/%3E%3Cpath d='M167 150l6 0l-3 5z' class='doodle green'/%3E%3Cpath d='M170 157l0 1' class='doodle green'/%3E%3C!-- Stopwatch --%3E%3Ccircle cx='80' cy='160' r='6' class='doodle grey'/%3E%3Cpath d='M80 154l0 6l4 0' class='doodle grey'/%3E%3Cpath d='M78 148l4 0' class='doodle grey'/%3E%3C!-- Target/Dartboard --%3E%3Ccircle cx='120' cy='160' r='7' class='doodle green'/%3E%3Ccircle cx='120' cy='160' r='4' class='doodle green'/%3E%3Ccircle cx='120' cy='160' r='2' class='doodle green'/%3E%3C!-- Sneaker --%3E%3Cpath d='M45 180c0-2 2-3 5-3l8 0c3 0 5 1 5 3l0 3l-18 0z' class='doodle grey'/%3E%3Cpath d='M47 183l14 0l0 2l-14 0z' class='doodle grey'/%3E%3Cpath d='M50 177l0 3M53 177l0 3M56 177l0 3' class='doodle grey'/%3E%3C!-- Water Bottle --%3E%3Cpath d='M140 175l0 15l6 0l0-15M142 175l0-3l2 0l0 3' class='doodle green'/%3E%3Cpath d='M141 180l4 0M141 185l4 0' class='doodle green'/%3E%3C!-- Gym Weight Plate --%3E%3Ccircle cx='90' cy='185' r='6' class='doodle grey'/%3E%3Ccircle cx='90' cy='185' r='3' class='doodle grey'/%3E%3C!-- Sports Cap --%3E%3Cpath d='M160 180c0-3 4-5 8-5s8 2 8 5l-2 0l0 3l-12 0l0-3z' class='doodle green'/%3E%3Cpath d='M174 183l4 0' class='doodle green'/%3E%3C/svg%3E")`;

  return (
    <div className="min-h-screen bg-black relative">
      {/* Sports Doodle Background Pattern for Black Background */}
      <div
        className="absolute inset-0 opacity-[0.15] pointer-events-none"
        style={{
          backgroundImage: sportsDoodlePattern,
          backgroundSize: '200px 200px',
          backgroundRepeat: 'repeat'
        }}
      />
      <Header />
      
      <div className="bg-gradient-to-b from-emerald-900/20 to-black pt-20 pb-8 relative overflow-hidden">
        <div className="absolute inset-0 bg-[url('https://images.unsplash.com/photo-1485395037613-e83d5c1f5290?ixlib=rb-1.2.1&auto=format&fit=crop&w=1350&q=80')] opacity-5 bg-center bg-cover"></div>
        <div className="container mx-auto px-4 relative z-10">
          <h1 className={`${isMobile ? 'text-2xl' : 'text-4xl md:text-5xl'} font-bold text-white mb-2 text-center`}>
            Explore Venues
          </h1>
          <p className={`${isMobile ? 'text-sm' : 'text-xl'} text-gray-300 max-w-2xl mx-auto text-center mb-6`}>
            Find the perfect sports venue for your next game
          </p>
          
          <div className="max-w-2xl mx-auto">
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <Search className={`${isMobile ? 'h-4 w-4' : 'h-5 w-5'} text-gray-400`} />
              </div>
              <input
                type="text"
                placeholder="Search venues..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className={`pl-10 w-full ${isMobile ? 'p-3 text-sm' : 'p-4'} rounded-lg focus:outline-none focus:ring-2 focus:ring-emerald-500 bg-gray-900/80 backdrop-blur-sm text-white border border-emerald-900/30`}
              />
              <button
                onClick={() => setIsFilterOpen(!isFilterOpen)}
                className={`absolute inset-y-0 right-0 px-3 flex items-center bg-emerald-900/80 text-white rounded-r-lg hover:bg-emerald-800/80 transition-colors border border-emerald-900/30`}
              >
                <Filter className={`${isMobile ? 'h-4 w-4 mr-1' : 'h-5 w-5 mr-2'}`} />
                {!isMobile && 'Filters'}
              </button>
            </div>
            
            {isFilterOpen && (
              <div className="mt-4 bg-gray-900/90 backdrop-blur-md p-4 rounded-lg shadow-lg border border-emerald-900/30">
                <div className="mb-4">
                  <h3 className={`${isMobile ? 'text-sm' : 'text-lg'} font-semibold text-emerald-400 mb-3`}>Filter by Sport</h3>
                  <div className="flex flex-wrap gap-2">
                    {sports.map(sport => (
                      <button
                        key={sport.id}
                        onClick={() => toggleSportFilter(sport.id)}
                        className={`px-3 py-1 rounded-full ${isMobile ? 'text-xs' : 'text-sm'} ${
                          selectedSports.includes(sport.id)
                            ? 'bg-emerald-900/80 text-white'
                            : 'bg-gray-700/50 text-gray-300 hover:bg-gray-600/50'
                        } transition-colors border border-emerald-900/20`}
                      >
                        {sport.name}
                      </button>
                    ))}
                  </div>
                </div>
                
                <div className="mb-4">
                  <h3 className={`${isMobile ? 'text-sm' : 'text-lg'} font-semibold text-emerald-400 mb-3`}>Rating</h3>
                  <div className="flex items-center space-x-2">
                    {[0, 3, 3.5, 4, 4.5].map(rating => (
                      <button
                        key={rating}
                        onClick={() => setRatingFilter(rating)}
                        className={`px-3 py-1 rounded-md ${isMobile ? 'text-xs' : 'text-sm'} ${
                          ratingFilter === rating
                            ? 'bg-emerald-900/80 text-white'
                            : 'bg-gray-700/50 text-gray-300 hover:bg-gray-600/50'
                        } transition-colors border border-emerald-900/20`}
                      >
                        {rating === 0 ? 'Any' : `${rating}+`}
                      </button>
                    ))}
                  </div>
                </div>
                
                <div className="flex justify-end gap-2">
                  <button
                    onClick={clearFilters}
                    className={`px-3 py-1 text-gray-300 hover:text-emerald-400 transition-colors ${isMobile ? 'text-xs' : 'text-sm'}`}
                  >
                    Clear
                  </button>
                  <button
                    onClick={applyFilters}
                    className={`px-4 py-1 bg-emerald-900/80 text-white rounded-md hover:bg-emerald-800/80 transition-colors ${isMobile ? 'text-xs' : 'text-sm'}`}
                  >
                    Apply
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
      
      <div className="container mx-auto px-4 py-6">
        <div className="mb-4 flex justify-between items-center">
          <h2 className={`${isMobile ? 'text-lg' : 'text-2xl'} font-bold text-white`}>
            {loading ? 'Loading...' : `${filteredVenues.length} Venues`}
            {/* Show location text for URL, GPS and user preference, but not IP fallback */}
            {(smartLocation.source === 'url' || smartLocation.source === 'gps' || smartLocation.source === 'preference') && smartLocation.coordinates && (
              <span className="text-sm text-gray-400 ml-2">
                near {smartLocation.name || 'your location'}
              </span>
            )}
          </h2>
          <div className="flex space-x-2">
            {/* Show sort button for URL, GPS and user preference location, but not IP fallback */}
            {(smartLocation.source === 'url' || smartLocation.source === 'gps' || smartLocation.source === 'preference') && smartLocation.coordinates ? (
              <button
                onClick={toggleSortOption}
                className={`px-3 py-1.5 bg-emerald-900/80 text-white rounded-md hover:bg-emerald-800/80 transition-colors flex items-center ${isMobile ? 'text-xs' : 'text-sm'}`}
              >
                <ArrowUpDown className={`${isMobile ? 'h-3 w-3 mr-1' : 'h-4 w-4 mr-2'}`} />
                {isMobile ? (sortOption === 'distance' ? 'Rating' : 'Distance') : `Sort by ${sortOption === 'distance' ? 'Rating' : 'Distance'}`}
              </button>
            ) : (
              <div className={`px-3 py-1.5 bg-emerald-900/80 text-white rounded-md flex items-center ${isMobile ? 'text-xs' : 'text-sm'}`}>
                <Star className={`${isMobile ? 'h-3 w-3 mr-1' : 'h-4 w-4 mr-2'}`} />
                {isMobile ? 'Rating' : 'Sort by Rating'}
              </div>
            )}
            
            {!hasPermission && (
              <button
                onClick={enableLocation}
                className={`px-3 py-1.5 border border-emerald-700/50 text-emerald-400 rounded-md hover:bg-emerald-900/20 transition-colors flex items-center ${isMobile ? 'text-xs' : 'text-sm'}`}
              >
                <Navigation className={`${isMobile ? 'h-3 w-3 mr-1' : 'h-4 w-4 mr-2'}`} />
                {!isMobile && 'Location'}
              </button>
            )}
          </div>
        </div>
        
        {loading || smartLocation.isLoading ? (
          <div className="flex justify-center items-center py-12">
            <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-emerald-500"></div>
            <span className="ml-3 text-white">
              {smartLocation.isLoading ? 'Resolving location...' : 'Loading venues...'}
            </span>
          </div>
        ) : filteredVenues.length === 0 ? (
          <div className="text-center py-12 bg-gray-900/30 rounded-xl border border-emerald-900/20">
            <h3 className={`${isMobile ? 'text-lg' : 'text-2xl'} font-semibold text-white mb-2`}>No venues found</h3>
            <p className={`text-gray-300 mb-4 ${isMobile ? 'text-sm' : ''}`}>Try adjusting your search</p>
            <button
              onClick={clearFilters}
              className={`px-4 py-2 bg-emerald-900/80 text-white rounded-md hover:bg-emerald-800/80 transition-colors ${isMobile ? 'text-sm' : ''}`}
            >
              Clear Filters
            </button>
          </div>
        ) : (
          <div>
            {/* Smart Location Indicator - Show for URL, GPS and user preference, but not IP fallback */}
            {(smartLocation.source === 'url' || smartLocation.source === 'gps' || smartLocation.source === 'preference') && smartLocation.coordinates && smartLocation.name && (
              <div className="mb-4 p-3 bg-emerald-900/20 border border-emerald-700/30 rounded-lg">
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <MapPin className="h-4 w-4 text-emerald-400 mr-2" />
                    <span className="text-emerald-300 text-sm">
                      {smartLocation.source === 'url' && 'Showing venues near: '}
                      {smartLocation.source === 'preference' && 'Showing venues near your preferred location: '}
                      {smartLocation.source === 'gps' && 'Showing venues near your current location: '}
                      <span className="font-semibold text-white">{smartLocation.name}</span>
                    </span>
                  </div>
                  <div className="flex gap-2">
                    {smartLocation.source === 'preference' && (
                      <button
                        onClick={() => navigate('/profile')}
                        className="text-xs text-gray-400 hover:text-white transition-colors"
                      >
                        Change preference
                      </button>
                    )}
                    <button
                      onClick={() => navigate('/venues')}
                      className="text-xs text-gray-400 hover:text-white transition-colors"
                    >
                      Clear location
                    </button>
                  </div>
                </div>
              </div>
            )}

            <div className={`${isMobile ? 'space-y-4' : 'grid gap-3 grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4'}`}>
            {filteredVenues.map((venue) => (
              <div
                key={venue.id}
                onClick={() => navigate(`/venues/${venue.id}`)}
                className={`${
                  isMobile
                    ? 'bg-gray-900/50 rounded-2xl overflow-hidden shadow-xl border border-emerald-900/20 hover:border-emerald-700/50 backdrop-blur-sm cursor-pointer transition-all duration-300 hover:shadow-2xl hover:bg-gray-900/70'
                    : 'bg-gray-900/50 rounded-lg overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 border border-emerald-900/20 hover:border-emerald-700/50 h-full flex flex-col group backdrop-blur-sm cursor-pointer'
                }`}
                role="button"
                tabIndex={0}
                aria-label={`View details for ${venue.name} - Rating: ${venue.rating?.toFixed(1) || '4.5'}, Location: ${venue.location}`}
                onKeyDown={(e) => {
                  if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    navigate(`/venues/${venue.id}`);
                  }
                }}
              >
                {isMobile ? (
                  // Mobile-First Compact Card Design
                  <div className="p-4">
                    <div className="flex items-center mb-3">
                      <div className="w-14 h-14 rounded-xl overflow-hidden flex-shrink-0 mr-3 shadow-md">
                        <LazyImage
                          src={venue.image_url || 'https://images.unsplash.com/photo-1574629810360-7efbbe195018?q=80&w=1000'}
                          alt={`${venue.name} - Sports venue in ${venue.location}`}
                          className="w-full h-full object-cover"
                          sizes="56px"
                          style={{ aspectRatio: '1/1' }}
                        />
                      </div>
                      <div className="flex-1 min-w-0">
                        <h3 className="text-lg font-bold text-white mb-1 line-clamp-1">
                          {venue.name}
                        </h3>
                        <div className="flex items-center gap-1 mb-2">
                          <MapPin className="w-3 h-3 text-gray-400 flex-shrink-0" />
                          <span className="text-xs text-gray-300 truncate">{venue.location}</span>
                        </div>
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-3">
                            <div className="flex items-center">
                              <Star className="h-3 w-3 text-yellow-400 fill-yellow-400 mr-1" />
                              <span className="text-xs font-semibold text-white">{venue.rating?.toFixed(1) || '4.5'}</span>
                            </div>
                            {venue.distance !== null && (
                              <div className="flex items-center">
                                <Navigation className="w-3 h-3 text-emerald-400 mr-1" />
                                <span className="text-xs font-medium text-emerald-400">
                                  {venue.distance < 1
                                    ? `${(venue.distance * 1000).toFixed(0)}m`
                                    : `${venue.distance.toFixed(1)}km`}
                                </span>
                              </div>
                            )}
                          </div>
                          <div className="flex items-center text-emerald-400 font-medium">
                            <span className="text-sm mr-1">Details</span>
                            <span className="text-lg">→</span>
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Discount Badge for Mobile - Compact */}
                    {venueDiscounts[venue.id] && (
                      <div className="mb-2">
                        <DiscountBadge discount={venueDiscounts[venue.id]} />
                      </div>
                    )}
                  </div>
                ) : (
                  // Desktop Design (unchanged)
                  <>
                    <div className="h-36 relative overflow-hidden flex-shrink-0">
                      <LazyImage
                        src={venue.image_url || 'https://images.unsplash.com/photo-1574629810360-7efbbe195018?q=80&w=1000'}
                        alt={`${venue.name} - Sports venue in ${venue.location}`}
                        className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-500"
                        sizes="(max-width: 640px) 50vw, (max-width: 1024px) 33vw, 25vw"
                        style={{ aspectRatio: '16/9' }}
                      />
                      <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent"></div>

                      {/* Discount Badge */}
                      {venueDiscounts[venue.id] && (
                        <DiscountBadge discount={venueDiscounts[venue.id]} />
                      )}

                      <div className="absolute top-2 right-2 bg-emerald-900/80 backdrop-blur px-2 py-0.5 rounded-full shadow flex items-center">
                        <Star className="h-3 w-3 text-yellow-400 fill-yellow-400 mr-1" />
                        <span className="text-xs font-bold text-white">{venue.rating?.toFixed(1) || '4.5'}</span>
                      </div>
                    </div>

                    <div className="p-4 flex flex-col flex-grow">
                      <div className="flex-1">
                        <h3 className="text-lg font-bold text-white mb-1 line-clamp-1 group-hover:text-emerald-400 transition-colors">
                          {venue.name}
                        </h3>

                        <div className="flex items-center gap-1 mb-1">
                          <MapPin className="w-3.5 h-3.5 text-gray-400 flex-shrink-0" />
                          <span className="text-sm text-gray-300 truncate">{venue.location}</span>
                        </div>

                        {venue.distance !== null && (
                          <div className="mb-2 flex items-center">
                            <Navigation className="w-3.5 h-3.5 text-emerald-400 mr-1" />
                            <span className="text-sm text-gray-300">
                              {venue.distance < 1
                                ? `${(venue.distance * 1000).toFixed(0)} m away`
                                : `${venue.distance.toFixed(1)} km away`}
                            </span>
                          </div>
                        )}

                        {venue.facilities && venue.facilities.length > 0 && (
                          <div className="flex flex-wrap gap-1 mb-2">
                            {venue.facilities.slice(0, 2).map(facility => (
                              <span
                                key={facility}
                                className="inline-block text-xs bg-emerald-900/30 text-emerald-300 px-2 py-0.5 rounded-full border border-emerald-700/30"
                              >
                                {facility}
                              </span>
                            ))}
                            {venue.facilities.length > 2 && (
                              <span className="inline-block text-xs text-gray-400">
                                +{venue.facilities.length - 2}
                              </span>
                            )}
                          </div>
                        )}
                      </div>

                      <div className="mt-auto pt-3 flex justify-end">
                        <span className="text-emerald-400 text-xs font-medium group-hover:translate-x-1 transition-transform duration-300">
                          View Details →
                        </span>
                      </div>
                    </div>
                  </>
                )}
              </div>
            ))}
          </div>
          </div>
        )}
      </div>
      
      {/* BookSlotModal removed - now using BookingPage navigation */}
    </div>
  );
};

export default Venues;
