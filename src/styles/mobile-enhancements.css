/* Mobile-First Enhancements for Grid२Play */

/* Ensure all touch targets meet 48px minimum */
@media (max-width: 768px) {
  button, 
  [role="button"], 
  .touch-target {
    min-height: 48px;
    min-width: 48px;
  }

  /* Mobile card shadows and animations */
  .mobile-card {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    transition: all 0.3s ease;
  }

  .mobile-card:hover {
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.25);
    transform: translateY(-2px);
  }

  /* Mobile-specific spacing */
  .mobile-container {
    padding: 16px;
    gap: 16px;
  }

  /* Mobile typography optimizations */
  .mobile-title {
    font-size: 1.25rem;
    font-weight: 700;
    line-height: 1.4;
  }

  .mobile-subtitle {
    font-size: 0.875rem;
    line-height: 1.5;
  }

  /* Mobile button styles */
  .mobile-button-primary {
    background: rgba(6, 95, 70, 0.8);
    color: white;
    border-radius: 12px;
    padding: 12px 16px;
    font-weight: 600;
    min-height: 48px;
    width: 100%;
    transition: all 0.2s ease;
  }

  .mobile-button-primary:hover {
    background: rgba(6, 95, 70, 0.9);
  }

  .mobile-button-secondary {
    border: 2px solid rgba(6, 95, 70, 0.5);
    color: rgb(52, 211, 153);
    background: transparent;
    border-radius: 12px;
    padding: 12px 16px;
    font-weight: 600;
    min-height: 48px;
    width: 100%;
    transition: all 0.2s ease;
  }

  .mobile-button-secondary:hover {
    background: rgba(6, 95, 70, 0.1);
  }

  /* Mobile-specific animations */
  .mobile-fade-in {
    animation: mobileSlideUp 0.3s ease-out;
  }

  @keyframes mobileSlideUp {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  /* Mobile pull-to-refresh indicator */
  .mobile-pull-indicator {
    position: absolute;
    top: -40px;
    left: 50%;
    transform: translateX(-50%);
    opacity: 0;
    transition: opacity 0.2s ease;
  }

  .mobile-pull-indicator.active {
    opacity: 1;
  }

  /* Mobile loading states */
  .mobile-skeleton {
    background: linear-gradient(90deg, 
      rgba(55, 65, 81, 0.3) 25%, 
      rgba(75, 85, 99, 0.3) 50%, 
      rgba(55, 65, 81, 0.3) 75%
    );
    background-size: 200% 100%;
    animation: mobileShimmer 1.5s infinite;
  }

  @keyframes mobileShimmer {
    0% {
      background-position: -200% 0;
    }
    100% {
      background-position: 200% 0;
    }
  }

  /* Mobile-specific focus states */
  .mobile-focus:focus {
    outline: 2px solid rgb(52, 211, 153);
    outline-offset: 2px;
  }

  /* Mobile swipe indicators */
  .mobile-swipe-indicator {
    position: relative;
  }

  .mobile-swipe-indicator::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 50%;
    transform: translateX(-50%);
    width: 40px;
    height: 4px;
    background: rgba(52, 211, 153, 0.3);
    border-radius: 2px;
  }

  /* Mobile-optimized scrollbars */
  .mobile-scroll::-webkit-scrollbar {
    width: 4px;
  }

  .mobile-scroll::-webkit-scrollbar-track {
    background: rgba(55, 65, 81, 0.3);
  }

  .mobile-scroll::-webkit-scrollbar-thumb {
    background: rgba(52, 211, 153, 0.5);
    border-radius: 2px;
  }

  /* Mobile-specific grid layouts */
  .mobile-grid-1 {
    display: grid;
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .mobile-grid-2 {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 12px;
  }

  .mobile-grid-3 {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 8px;
  }

  /* Mobile-specific text truncation */
  .mobile-truncate-1 {
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .mobile-truncate-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  /* Mobile-specific spacing utilities */
  .mobile-space-y-4 > * + * {
    margin-top: 16px;
  }

  .mobile-space-y-6 > * + * {
    margin-top: 24px;
  }

  /* Mobile-specific border radius */
  .mobile-rounded-lg {
    border-radius: 16px;
  }

  .mobile-rounded-xl {
    border-radius: 20px;
  }

  /* Mobile-specific backdrop blur */
  .mobile-backdrop-blur {
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
  }

  /* Mobile-specific glassmorphism */
  .mobile-glass {
    background: rgba(17, 24, 39, 0.7);
    backdrop-filter: blur(12px);
    -webkit-backdrop-filter: blur(12px);
    border: 1px solid rgba(52, 211, 153, 0.1);
  }

  /* Mobile-specific safe areas */
  .mobile-safe-top {
    padding-top: env(safe-area-inset-top);
  }

  .mobile-safe-bottom {
    padding-bottom: env(safe-area-inset-bottom);
  }

  /* Mobile-specific keyboard handling */
  .mobile-keyboard-adjust {
    padding-bottom: env(keyboard-inset-height, 0px);
  }
}

/* Reduce motion for users who prefer it */
@media (prefers-reduced-motion: reduce) {
  .mobile-card,
  .mobile-button-primary,
  .mobile-button-secondary,
  .mobile-fade-in {
    animation: none;
    transition: none;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .mobile-card {
    border: 2px solid currentColor;
  }
  
  .mobile-button-primary,
  .mobile-button-secondary {
    border: 2px solid currentColor;
  }
}
