
@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom animations for enhanced UI */
@keyframes shine {
  0% {
    transform: translateX(-100%) skewX(-12deg);
  }
  100% {
    transform: translateX(200%) skewX(-12deg);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

@keyframes float-medium {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  33% {
    transform: translateY(-15px) rotate(2deg);
  }
  66% {
    transform: translateY(-10px) rotate(-1deg);
  }
}

@keyframes float-slow {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-25px) rotate(3deg);
  }
}

@keyframes glow {
  0%, 100% {
    box-shadow: 0 0 20px rgba(46, 125, 50, 0.3);
  }
  50% {
    box-shadow: 0 0 40px rgba(46, 125, 50, 0.6);
  }
}

@keyframes scroll-left {
  0% {
    transform: translateX(100%);
  }
  100% {
    transform: translateX(-100%);
  }
}

@keyframes gradient-x {
  0%, 100% {
    background-size: 200% 200%;
    background-position: left center;
  }
  50% {
    background-size: 200% 200%;
    background-position: right center;
  }
}

.animate-shine {
  animation: shine 2s infinite;
}

.animate-float {
  animation: float 6s ease-in-out infinite;
}

.animate-float-medium {
  animation: float-medium 8s ease-in-out infinite;
}

.animate-float-slow {
  animation: float-slow 10s ease-in-out infinite;
}

.animate-float-fast {
  animation: float 4s ease-in-out infinite;
}

.animate-glow {
  animation: glow 2s ease-in-out infinite;
}

.animate-scroll-left {
  animation: scroll-left 30s linear infinite;
}

.animate-gradient-x {
  animation: gradient-x 3s ease infinite;
}

@layer base {
  :root {
    --background: 224 71.4% 4.1%;
    --foreground: 210 20% 98%;
    --card: 224 71.4% 4.1%;
    --card-foreground: 210 20% 98%;
    --popover: 224 71.4% 4.1%;
    --popover-foreground: 210 20% 98%;
    --primary: 263.4 70% 50.4%;
    --primary-foreground: 210 20% 98%;
    --secondary: 215 27.9% 16.9%;
    --secondary-foreground: 210 20% 98%;
    --muted: 215 27.9% 16.9%;
    --muted-foreground: 217.9 10.6% 64.9%;
    --accent: 215 27.9% 16.9%;
    --accent-foreground: 210 20% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 20% 98%;
    --border: 215 27.9% 16.9%;
    --input: 215 27.9% 16.9%;
    --ring: 263.4 70% 50.4%;
    --radius: 0.75rem;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* Add animation for typing indicator */
.dot-typing {
  position: relative;
  left: -9999px;
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background-color: #8B5CF6;
  color: #8B5CF6;
  box-shadow: 9999px 0 0 0 #8B5CF6;
  animation: dot-typing 1.5s infinite linear;
}

.dot-typing::before,
.dot-typing::after {
  content: '';
  display: inline-block;
  position: absolute;
  top: 0;
}

.dot-typing::before {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background-color: #8B5CF6;
  color: #8B5CF6;
  animation: dot-typing-before 1.5s infinite linear;
  animation-delay: 0.25s;
}

.dot-typing::after {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background-color: #8B5CF6;
  color: #8B5CF6;
  animation: dot-typing-after 1.5s infinite linear;
  animation-delay: 0.5s;
}

@keyframes dot-typing {
  0% {
    box-shadow: 9999px 0 0 0 #8B5CF6;
  }
  25% {
    box-shadow: 9999px 0 0 0 rgba(139, 92, 246, 0.4);
  }
  50% {
    box-shadow: 9999px 0 0 0 rgba(139, 92, 246, 0.2);
  }
  75% {
    box-shadow: 9999px 0 0 0 rgba(139, 92, 246, 0.1);
  }
  100% {
    box-shadow: 9999px 0 0 0 #8B5CF6;
  }
}

@keyframes dot-typing-before {
  0% {
    box-shadow: 9984px 0 0 0 #8B5CF6;
  }
  25% {
    box-shadow: 9984px 0 0 0 rgba(139, 92, 246, 0.4);
  }
  50% {
    box-shadow: 9984px 0 0 0 rgba(139, 92, 246, 0.2);
  }
  75% {
    box-shadow: 9984px 0 0 0 rgba(139, 92, 246, 0.1);
  }
  100% {
    box-shadow: 9984px 0 0 0 #8B5CF6;
  }
}

@keyframes dot-typing-after {
  0% {
    box-shadow: 9969px 0 0 0 #8B5CF6;
  }
  25% {
    box-shadow: 9969px 0 0 0 rgba(139, 92, 246, 0.4);
  }
  50% {
    box-shadow: 9969px 0 0 0 rgba(139, 92, 246, 0.2);
  }
  75% {
    box-shadow: 9969px 0 0 0 rgba(139, 92, 246, 0.1);
  }
  100% {
    box-shadow: 9969px 0 0 0 #8B5CF6;
  }
}

/* Hero video styles */
.hero-section {
  position: relative;
  overflow: hidden;
}

.hero-video {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.hero-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(to bottom, rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.8));
}

.dark-gradient-overlay {
  background: linear-gradient(to bottom, rgba(0, 0, 0, 0.3), rgba(0, 0, 0, 0.9));
}

.hero-content {
  position: relative;
  z-index: 10;
  text-align: center;
  padding: 0 20px;
}

/* Buttons */
.dynamic-button {
  padding: 0.75rem 1.5rem;
  font-weight: 600;
  border-radius: 0.5rem;
  background: linear-gradient(to right, #6366f1, #4f46e5);
  color: white;
  transition: all 0.3s ease;
  box-shadow: 0 10px 25px rgba(99, 102, 241, 0.3);
}

.dynamic-button:hover {
  box-shadow: 0 15px 30px rgba(99, 102, 241, 0.4);
  transform: translateY(-2px);
}

.nike-button {
  padding: 0.75rem 1.5rem;
  font-weight: 600;
  border-radius: 0.5rem;
  transition: all 0.3s ease;
}

/* Animations */
@keyframes fade-in {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in {
  animation: fade-in 0.5s forwards;
}

@keyframes reveal {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-reveal {
  animation: reveal 0.8s forwards;
}

.section-title {
  font-size: 2rem;
  font-weight: 700;
}

/* Glass card effect */
.glass-card {
  background-color: rgba(15, 23, 42, 0.6);
  backdrop-filter: blur(12px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 1rem;
}

/* Pattern background */
.pattern-dots {
  background-image: radial-gradient(rgba(255, 255, 255, 0.1) 1px, transparent 1px);
  background-size: 20px 20px;
}

/* Infinite scroll animation for partners section */
@keyframes infinite-scroll {
  from { transform: translateX(0); }
  to { transform: translateX(-50%); }
}

.animate-infinite-scroll {
  animation: infinite-scroll 30s linear infinite;
}

/* Pulse animation for green dots */
@keyframes pulse-light {
  0% { opacity: 1; }
  50% { opacity: 0.3; }
  100% { opacity: 1; }
}

.animate-pulse-light {
  animation: pulse-light 3s infinite;
}

/* Custom Sport Cards */
.venue-card, .sport-card {
  border: 1px solid rgba(255, 255, 255, 0.05);
  transition: all 0.3s ease;
}

.venue-card:hover, .sport-card:hover {
  border-color: rgba(99, 102, 241, 0.3);
}

/* Mobile Header Performance Optimizations */
.mobile-header {
  will-change: transform;
  backface-visibility: hidden;
}

/* Mobile Menu Transitions */
.mobile-menu {
  transform: translateX(-100%);
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  opacity: 0;
  visibility: hidden;
}

.mobile-menu.mobile-menu-open {
  transform: translateX(0);
  opacity: 1;
  visibility: visible;
}

/* Mobile Menu Button Enhancements */
.mobile-menu-button {
  touch-action: manipulation;
  -webkit-tap-highlight-color: transparent;
}

/* Mobile Touch Targets - Ensure 48px minimum */
@media (max-width: 768px) {
  .mobile-menu a,
  .mobile-menu button {
    min-height: 48px;
    display: flex;
    align-items: center;
    touch-action: manipulation;
    -webkit-tap-highlight-color: transparent;
  }

  /* Improve mobile menu spacing */
  .mobile-menu .container {
    padding-bottom: env(safe-area-inset-bottom, 0px);
  }
}

/* Mobile Booking Flow Optimizations */
.booking-form-container {
  padding-bottom: env(keyboard-inset-height, 0px);
}

/* Mobile Form Input Optimizations */
@media (max-width: 768px) {
  .booking-form-container input,
  .booking-form-container select,
  .booking-form-container button {
    min-height: 48px;
    font-size: 16px; /* Prevent zoom on iOS */
    touch-action: manipulation;
    -webkit-tap-highlight-color: transparent;
  }

  /* Mobile slot grid optimizations */
  .mobile-slot-grid {
    gap: 12px; /* Adequate spacing between touch targets */
  }

  .mobile-slot-grid button {
    min-height: 48px;
    touch-action: manipulation;
    -webkit-tap-highlight-color: transparent;
  }
}

/* Enhanced Mobile Error Messages */
.mobile-error-message {
  animation: slideInFromTop 0.3s ease-out;
}

@keyframes slideInFromTop {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Mobile Keyboard Handling */
@media (max-width: 768px) {
  /* Adjust form container when keyboard is visible */
  .booking-form-container {
    transition: padding-bottom 0.3s ease;
  }

  /* Ensure error messages are visible above keyboard */
  .mobile-error-message {
    position: relative;
    z-index: 10;
    margin-bottom: 8px;
  }

  /* Improve focus states for mobile */
  input:focus,
  select:focus,
  button:focus {
    outline: 2px solid #10b981;
    outline-offset: 2px;
  }
}

/* Reduce Motion for Accessibility and Performance */
@media (prefers-reduced-motion: reduce) {
  /* Disable all animations for users who prefer reduced motion */
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }

  /* Specifically disable complex animations */
  .animate-float,
  .animate-float-medium,
  .animate-float-slow,
  .animate-float-fast,
  .animate-glow,
  .animate-gradient-x,
  .animate-scroll-left,
  .animate-infinite-scroll,
  .animate-pulse-light,
  .animate-shine {
    animation: none !important;
  }

  /* Disable framer-motion animations */
  [data-framer-motion] {
    transform: none !important;
    animation: none !important;
  }
}

/* Mobile Performance Optimizations */
@media (max-width: 768px) {
  /* Reduce animation complexity on mobile */
  .hero-animations {
    will-change: auto;
  }

  /* Faster animations on mobile for better performance */
  .animate-fade-in,
  .animate-reveal {
    animation-duration: 0.3s;
  }

  /* Optimize image rendering */
  img {
    image-rendering: optimizeSpeed;
    image-rendering: -webkit-optimize-contrast;
  }

  /* Reduce blur effects on mobile for performance */
  .blur-3xl {
    backdrop-filter: blur(8px);
  }

  .backdrop-blur-sm {
    backdrop-filter: blur(2px);
  }

  .backdrop-blur-md {
    backdrop-filter: blur(4px);
  }
}

/* Content Visibility Optimization */
.venue-card,
.sport-card,
.tournament-card {
  content-visibility: auto;
  contain-intrinsic-size: 300px;
}

/* Intersection Observer Optimization */
.lazy-load-container {
  min-height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
}

@keyframes blink {
  0%, 100% { opacity: 1 }
  50% { opacity: 0 }
}
.animate-blink {
  animation: blink 1s step-end infinite;
}

