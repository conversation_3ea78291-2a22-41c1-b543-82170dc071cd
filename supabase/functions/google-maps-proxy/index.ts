import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2';

// CORS headers
const corsHeaders = {
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Headers": "authorization, x-client-info, apikey, content-type",
  "Access-Control-Allow-Methods": "GET, POST, OPTIONS",
};

// Types for Google Maps API
interface PlacesAutocompleteRequest {
  input: string;
  types?: string[];
  componentRestrictions?: { country: string };
  bounds?: {
    northeast: { lat: number; lng: number };
    southwest: { lat: number; lng: number };
  };
}

interface PlaceDetailsRequest {
  place_id: string;
  fields?: string[];
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    // Verify authentication - simplified approach
    const authHeader = req.headers.get('Authorization');
    const apiKeyHeader = req.headers.get('apikey');

    console.log('Auth header present:', !!authHeader);
    console.log('API key header present:', !!apiKeyHeader);

    // Check if we have either auth header or valid API key
    if (!authHeader && !apiKeyHeader) {
      console.error('Missing authorization');
      return new Response(
        JSON.stringify({ error: 'Missing authorization' }),
        { status: 401, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    // Initialize Supabase client for logging
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    );

    // For now, we'll use a simplified auth check
    // If API key matches our anon key, allow the request
    const validApiKey = Deno.env.get('SUPABASE_ANON_KEY');
    let userId = 'anonymous_user'; // Default user ID for logging

    if (apiKeyHeader && apiKeyHeader === validApiKey) {
      console.log('Valid API key authentication');
      userId = 'api_key_user';
    } else if (authHeader) {
      console.log('Bearer token provided, proceeding with request');
      // Extract a simple user identifier from token for logging (without full verification)
      const token = authHeader.replace('Bearer ', '');
      try {
        // Try to decode JWT payload for user ID (without verification)
        const payload = JSON.parse(atob(token.split('.')[1]));
        userId = payload.sub || payload.user_id || 'token_user';
        console.log('Extracted user ID from token:', userId);
      } catch (e) {
        console.log('Could not extract user ID from token, using default');
        userId = 'bearer_token_user';
      }
    } else {
      console.error('No valid authentication method');
      return new Response(
        JSON.stringify({ error: 'Invalid authentication' }),
        { status: 401, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    console.log('Authentication successful for user:', userId);

    // Get Google Maps API key from environment
    const googleMapsApiKey = Deno.env.get('GOOGLE_MAPS_API_KEY');
    if (!googleMapsApiKey) {
      console.error('Google Maps API key not configured');
      return new Response(
        JSON.stringify({ error: 'Google Maps service not configured' }),
        { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    const { action, ...requestData } = await req.json();

    // Handle different Google Maps API actions
    switch (action) {
      case 'autocomplete':
        return await handleAutocomplete(requestData as PlacesAutocompleteRequest, googleMapsApiKey, userId, supabaseClient);

      case 'place_details':
        return await handlePlaceDetails(requestData as PlaceDetailsRequest, googleMapsApiKey, userId, supabaseClient);
      
      default:
        return new Response(
          JSON.stringify({ error: 'Invalid action specified' }),
          { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
        );
    }

  } catch (error) {
    console.error('Google Maps proxy error:', error);
    return new Response(
      JSON.stringify({ error: 'Internal server error' }),
      { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    );
  }
});

// Handle Places Autocomplete requests
async function handleAutocomplete(
  request: PlacesAutocompleteRequest, 
  apiKey: string, 
  userId: string, 
  supabaseClient: any
) {
  try {
    console.log('Autocomplete request received:', JSON.stringify(request, null, 2));

    // Validate input
    if (!request.input || request.input.trim().length === 0) {
      throw new Error('Input is required for autocomplete');
    }

    // Build Google Maps Autocomplete API URL
    const params = new URLSearchParams({
      input: request.input.trim(),
      key: apiKey,
      types: request.types?.join('|') || 'geocode',
      components: request.componentRestrictions?.country ? `country:${request.componentRestrictions.country}` : 'country:IN',
    });

    // Add bounds if provided (for Delhi NCR restriction)
    if (request.bounds && request.bounds.northeast && request.bounds.southwest) {
      const { northeast, southwest } = request.bounds;
      if (northeast.lat && northeast.lng && southwest.lat && southwest.lng) {
        params.append('bounds', `${southwest.lat},${southwest.lng}|${northeast.lat},${northeast.lng}`);
        params.append('strictbounds', 'false'); // Allow some flexibility
        console.log('Added bounds to request:', `${southwest.lat},${southwest.lng}|${northeast.lat},${northeast.lng}`);
      } else {
        console.log('Invalid bounds data, skipping bounds parameter');
      }
    } else {
      console.log('No bounds provided, using default search area');
    }

    const url = `https://maps.googleapis.com/maps/api/place/autocomplete/json?${params.toString()}`;

    // Make request to Google Maps API
    const response = await fetch(url);
    const data = await response.json();

    // Log API usage for monitoring
    await supabaseClient.from('security_logs').insert({
      event_type: 'GOOGLE_MAPS_AUTOCOMPLETE_REQUEST',
      severity: 'LOW',
      details: {
        user_id: userId,
        query_length: request.input.length,
        results_count: data.predictions?.length || 0,
        timestamp: new Date().toISOString()
      },
      user_id: userId
    });

    return new Response(
      JSON.stringify(data),
      { status: 200, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    );

  } catch (error) {
    console.error('Autocomplete request failed:', error);
    return new Response(
      JSON.stringify({ error: 'Autocomplete request failed' }),
      { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    );
  }
}

// Handle Place Details requests
async function handlePlaceDetails(
  request: PlaceDetailsRequest, 
  apiKey: string, 
  userId: string, 
  supabaseClient: any
) {
  try {
    // Build Google Maps Place Details API URL
    const params = new URLSearchParams({
      place_id: request.place_id,
      key: apiKey,
      fields: request.fields?.join(',') || 'formatted_address,geometry,name,place_id,types',
    });

    const url = `https://maps.googleapis.com/maps/api/place/details/json?${params.toString()}`;

    // Make request to Google Maps API
    const response = await fetch(url);
    const data = await response.json();

    // Log API usage for monitoring
    await supabaseClient.from('security_logs').insert({
      event_type: 'GOOGLE_MAPS_PLACE_DETAILS_REQUEST',
      severity: 'LOW',
      details: {
        user_id: userId,
        place_id: request.place_id,
        timestamp: new Date().toISOString()
      },
      user_id: userId
    });

    return new Response(
      JSON.stringify(data),
      { status: 200, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    );

  } catch (error) {
    console.error('Place details request failed:', error);
    return new Response(
      JSON.stringify({ error: 'Place details request failed' }),
      { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    );
  }
}
